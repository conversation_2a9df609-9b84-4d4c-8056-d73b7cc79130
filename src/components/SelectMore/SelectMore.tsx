import React, { useState } from 'react'
import { Select } from 'antd'
import { SelectProps } from 'antd/es/select'

export function SelectMore(props: SelectProps<any>) {
    const { style, onDropdownVisibleChange, onFocus, onBlur, onSelect, dropdownStyle, ...others } = props
    const selectRef = React.useRef<any>()
    const [width, setwidth] = useState<number>(160)
    const [open, setOpen] = useState<boolean>(false)
    
    return <Select
        ref={selectRef}
        {...others}
        style={{ ...style, width, transition: 'width .3s' }}
        dropdownStyle={{ ...dropdownStyle, width }}
        onFocus={(e) => {
            setTimeout(() => setOpen(true), 350)
            setwidth(260)
            onFocus?.(e)
        }}
        onBlur={(e) => {
            setOpen(false)
            setwidth(160)
            onBlur?.(e)
        }}
        onSelect={(value: any, option: any) => {
            onSelect?.(value, option)
            if (open) {
                selectRef.current?.blur()
            }
        }}
        open={open}
        dropdownMatchSelectWidth={false}
        onDropdownVisibleChange={(visible) => {
            onDropdownVisibleChange?.(visible)
        }}
    />
} 