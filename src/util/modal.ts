import { Modal } from 'antd';
import i18n from 'i18next';

export const showConfirmModal = ({
  title = i18n.t('common.modal.delete.content'),
  onOk,
  okText = i18n.t('common.btn.confirm'),
  cancelText = i18n.t('common.btn.cancel'),
}: {
  title?: string;
  onOk: () => void;
  okText?: string;
  cancelText?: string;
}) => {
  Modal.confirm({
    centered: true,
    title,
    okText,
    cancelText,
    onOk
  });
};