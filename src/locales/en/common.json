{"common.noData": "No data available", "common.btn.packup": "Collapse", "common.btn.viewMore": "View more", "common.btn.view": "View", "common.modal.clear.content": "Are you sure you want to clear?", "common.btn.ok": "Confirm", "common.btn.cancel": "Cancel", "common.btn.clear": "Clear", "common.btn.delete": "Delete", "common.btn.collapseNode": "Collapse Node", "common.btn.refresh": "Refresh", "common.search.placeholder": "Please enter search content", "common.btn.moveout": "Move Out", "common.text.null": "None", "common.btn.batch.enable": "Batch Enable", "common.btn.batch.forbidden": "<PERSON>ch Disable", "common.text.password": "Password", "common.btn.batch.reset_password": "Batch Reset Password", "common.btn.normal": "Normal", "common.btn.lock": "Lock", "common.btn.edit": "Edit", "common.btn.copy": "<PERSON><PERSON>", "common.message.reset.success": "Reset Successful", "common.message.reset.error": "Reset Failed", "common.btn.resetPassword": "Reset Password", "common.message.delete_success": "Deletion Successful", "common.btn.batchText": "Batch Operation", "common.message.add.success": "Addition Successful", "common.message.edit.success": "Edit Successful", "common.search.select.placeholder": "Please Select", "common.search.input.placeholder": "Please Enter", "common.btn.save": "Save", "common.btn.saveAndAdd": "Save and Continue Adding", "common.text.rolesId": "System Role", "common.text.normalUser": "Regular User", "common.btn.confirm": "Confirm", "common.downloadTemplate.title": "Template File Download", "common.downloadTemplate.tip": "Download the template file and fill in the information according to the format in the Excel.", "common.downloadTemplate.download": "Download Template", "common.downloadTemplate.fileUpload": "File Upload", "common.downloadTemplate.fileUpload.tip": "Please upload the filled file here and click upload to generate.", "common.downloadTemplate.fileUpload.selectFile": "Select File", "common.downloadTemplate.fileUpload.selectFile.tip": "Please upload the batch generation connection file", "common.downloadTemplate.fileUpload.error": "File upload failed", "common.downloadTemplate.fileUpload.status1": "File Upload", "common.downloadTemplate.fileUpload.upload": "Upload", "common.downloadTemplate.fileUpload.status2": "Uploading File", "common.downloadTemplate.fileUpload.status3": "Upload Complete", "common.downloadTemplate.fileUpload.batchTip": "Batch Import {{val}}", "common.downloadTemplate.process.verify": "Verifying", "common.downloadTemplate.process.execute": "Executing", "common.downloadTemplate.process.success": "Success", "common.downloadTemplate.process.failed": "Failed", "common.text.finish": "Complete", "common.btn.yes": "Yes", "common.btn.no": "No", "common.btn.enable": "Enable", "common.btn.forbidden": "Disable", "common.text.action": "Action", "common.btn.add": "Add", "common.btn.append": "Add", "common.btn.open": "Open", "common.btn.close": "Close", "common.btn.next": "Next", "common.btn.reset.tip": "Are you sure you want to reset?", "common.message.new_success": "Added successfully", "common.btn.create": "Generate", "common.btn.preview": "Preview", "common.btn.query": "Query", "common.btn.reset.label": "Reset", "common.table.pagination.total": "Total {{total}} records", "common.btn.on": "Turn On", "common.btn.off": "Turn Off", "common.text.value": "Value", "common.text.state": "Variable", "common.message.saveSuccess": "Save Successful", "common.btn.pre": "Previous Step", "common.btn.duplication": "Copy", "common.text.delete.tip": "Are you sure you want to delete?", "common.btn.back": "Back", "common.btn.submit": "Submit", "common.numberDispaly": "{{val, number}}", "common.downloadTemplate.fileSize": "File Upload Size", "common.text.phoneMes": "SMS", "common.text.systemMes": "System Message", "common.text.emal": "Email", "common.text.enableOrNot": "Enable or Not", "common.message.error": "Edit Failed", "common.text.threshold": "<PERSON><PERSON><PERSON><PERSON>", "common.text.highLevel": "High Risk Level", "common.downloadTemplate.uploadErrorHanding": "Upload Error Handling", "common.downloadTemplate.uploadErrorHanding.hint": "Please select upload error handling", "common.downloadTemplate.terminate": "Terminate", "common.downloadTemplate.ignore": "Ignore", "common.downloadTemplate.batchUpload.tip": "Please upload the batch access rules file", "common.downloadTemplate.uploadErrorHanding.tip": "Please select an upload error handling method", "common.downloadTemplate.execute.result": "Total executed: {{total}}, Successful count: {{successCount}}, Failed count: {{errorCount}}, Failure details: {{errorMessage}}", "common.table.desc": "Remarks", "common.timeFormat": "YYYY-MM-DD HH:mm:ss", "common.table.updateAt": "Update Time", "common.downloadTemplate.selectAndDrag": "Click or drag files here to upload", "common.btn.upload": "Upload File", "common.modal.delete.content": "Are you sure you want to delete?", "common.message.export_success": "Export Successful", "common.btn.updateSuccess": "Upload Successful", "common.downloadTemplate.selectFile": "Please Select a File", "common.btn.download": "Download", "common.message.uploadSuccess": "Update Successful", "common.message.updateError": "Update Failed", "common.message.init": "Initializing", "common.btn.export": "Export", "common.text.cascadeRemoval": "Cascade Removal", "common.text.nonCascadeRemoval": "Non-Cascade Removal", "common.text.number": "Number", "common.text.containUpperLetters": "Uppercase Letters", "common.text.containSymbols": "Special Characters", "common.text.containLowerLetters": "Lowercase Letters", "common.text.sampleCount": "Sample Row Count", "common.text.sampleCount.tip": "Please enter the sample row count", "common.text.sampleRate": "Hit Rate", "common.text.sampleRate.tip": "Please enter the hit rate", "common.text.sampleCount.hint": "Please enter a value between 0-100", "common.message.editSuccess": "Modification Successful", "common.message.testSuccessfully": "Operation Successful", "common.message.operateSuccessfully": "Test Successful", "common.text.dba": "DBA Role", "common.text.checkAll": "Select All", "common.btn.modify": "Modify", "common.text.effectTimeTypeOption": {"8": "Every Day", "1": "Monday", "2": "Tuesday", "3": "Wednesday", "4": "Thursday", "5": "Friday", "6": "Saturday", "7": "Sunday"}, "common.search.selectDbElement": "Please select a database element", "common.downloadTemplate.execel.extra": "Download the template file and fill in the required format in Excel to generate connections.", "common.downloadTemplate.batchCreating": "Creating in batch", "common.downloadTemplate.createResult": "Creation result", "common.downloadTemplate.create.conn": "Create connection", "common.downloadTemplate.createEnd": "Creation ended", "common.downloadTemplate.creatingConn": "Creating connection", "common.btn.remove": "Remove", "common.message.removeSuccess": "Removal successful", "common.btn.viewDetail": "View Details", "common.btn.alert": "Modify", "common.btn.test": "Test", "common.btn.archive": "Archive", "common.btn.batchDelete": "<PERSON><PERSON> Delete", "common.btn.batchCreate": "Batch Create", "common.btn.manager": "Manage", "common.btn.batchRemove": "Batch Remove", "common.btn.batchSetPermLevel": "Batch Set Permission Level", "common.effectTime": {"day": "One Day", "week": "One Week", "month": "One Month", "forever": "Forever", "custom": "Custom Time Period"}, "common.text.forever": "Forever", "common.text.customTimePeriod": "Custom Time Period", "common.text.timeBucket": "Time Period:", "common.btn.addPerm": "Add Permission", "common.dictSearch.nodeName": {"table": "tables", "foreignTable": "foreignTable", "group": "views", "collection": "collections", "materializedView": "materializedViews", "keyGroup": "keys", "fileGroup": "files", "functionGroup": "functions", "procedureGroup": "procedures", "taskGroup": "jobs", "synonym": "synonym", "sequenceGroup": "sequences", "triggerGroup": "triggers", "databaseConnection": "DBLinks", "package": "packages", "packageBody": "packagesBody", "dictionaryGroup": "dictionaries", "gridFsBucket": "GridFsGroup", "userDefinedFunction": "userDefinedFunctions", "columnGroup": "columns", "indexGroup": "indexes", "constraintGroup": "constraints", "foreignKeyGroup": "foreignness", "partitionGroup": "parts", "syncMaterializedViews": "syncMaterializedViews", "asyncMaterializedViews": "asyncMaterializedViews", "importTaskGroup": "routineLoads"}, "common.btn.batchSetting": "Batch Set", "common.massage.delete.id": "Serial number", "common.massage.delete.failed": "Deletion Failed", "common.btn.clickUpload": "Click to Upload", "common.downloadTemplate.uploadFolder": "Please upload file", "common.btn.prePage": "Previous Page", "common.btn.nextPage": "Next Page", "common.message.exportCreated.tip": "The export task has been created", "common.formItem.input.plac": "Please Enter {{label}}", "common.timePeriod.last7Day": "Last 7 Days", "common.timePeriod.last15Day": "Last 15 Days", "common.timePeriod.last30Day": "Last 30 Days", "common.timePeriod.effectUserList": "Effective User List", "common.timePeriod.setttingSms": "Please configure the SMS channel", "common.timePeriod.settingEmail": "Please configure the email service", "common.serverError": "Server error occurred... Please try again later.", "common.downloadTemplate.userUpload.tip": "Please upload the user information file first"}