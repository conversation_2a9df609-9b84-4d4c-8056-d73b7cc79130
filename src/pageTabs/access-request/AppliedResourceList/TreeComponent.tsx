import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { ExclamationCircleOutlined, CloseOutlined } from '@ant-design/icons';
import * as _ from 'lodash';
import classnames from 'classnames'
import { Tree, Modal, Tooltip } from 'antd'
import { Iconfont } from 'src/components'
import { useSelector, useDispatch } from 'src/hook';
import { getExpandKeysAboutContainSearchValue } from 'src/util';
import {
  setLoadedTreeNodeMap,
  setLoadedKeys,
  getTreeNodeChildren,
  setSelectedNode,
  setExpandedKeys
} from './appliedResourceListSlice'
import { useTranslation } from 'react-i18next';
import styles from './index.module.scss'

interface IProps {
  searchValue?: string;
  permissionlist: any;
  flowMainUUID: string | undefined;
}


const TreeComponent = (props: IProps) => {
  const {
    searchValue,
    flowMainUUID,
    permissionlist,
  } = props

  const { isOnlyRead, roleNameList } = permissionlist || {}

  const { loadedKeys, newTreeData, expandedKeys, selectedTreeNode } = useSelector(state => state.appliedResourceList);

  const dispatch = useDispatch();
  const { t } = useTranslation()

  useEffect(() => {

    return () => {
      dispatch(setLoadedKeys([]));
      dispatch(setLoadedTreeNodeMap({}))
    }

  }, [])


  const handleSelect = (item: any[], info: any) => {

    if (!info.selected) {
      return;
    }
    dispatch(setSelectedNode(info?.node as any))
  }

  // 异步逐级加载数据 (连接及以下层级加载树内容)
  const handleLoadData = useCallback(async (node: any) => {
    if (node?.newNodeType === 'datasource') {
      return
    }

    const { id, roleId, nodePathWithType, nodeType, dataSourceType, connectionId, nodeName,
      nodePath, key
    } = node

    const params = {
      connectionId: connectionId || id,
      connectionType: dataSourceType,
      nodeType,
      nodeName,
      nodePath,
      nodePathWithType,
      roleId,
      key,
      flowMainUUID
    }
    await dispatch(getTreeNodeChildren(params))

  }, [dispatch])

  const generatorSearchTitle = (node: any) => {

    const { nodeName, title, nodeType, connection, testModel, dataSourceType } = node || {}
      ;
    return (
      <>
        <Iconfont
          className={classnames(styles.mr4, styles.color008dff, {
            [styles.colorf00]: testModel === 1 && nodeType === "connection",
            [styles.colorgreen]: !!testModel && testModel !== 1 && nodeType === "connection",
          })}
          type={`${nodeType === "datasource"
            ? `icon-connection-${nodeName}`
            : nodeType === "group"
              ? "icon-shujukuwenjianjia"
              : nodeType === "connection"
                ? `icon-${dataSourceType}`
                : `icon-${nodeType}`
            } `}
        />
        <span className={styles.titleTxt}>
          <Tooltip title={nodeType === "connection" &&
            <>
              {connection?.ip + ':' + connection?.port}
            </>}>
            {title}
          </Tooltip>
          {["datasource", "group"].includes(nodeType) && `(${node?.children?.length || 0})`}
        </span>
      </>
    );
  };

  // 渲染tree title完整内容
  const treeTitleRender = (node: any) => {
    const { id, nodeType, nodeName, dataSourceType, nodePathWithType } = node
    const result = (
      <div className={styles.treeTitleItem}>
        {generatorSearchTitle(node)}
      </div>
    );
    return result
  }

  const matchKeyword = (target = '', substring = '') => {
    if (!target) return false
    return target.toLowerCase().indexOf(substring.toLowerCase()) > -1
  }

  // /* 实现全局节点搜索 */
  const filterNodesNotMatch = useCallback(
    (nodes: any[]): any[] =>
      nodes.filter((node) => {

        //后端搜索 筛选信息只有一条， 前端筛选模糊匹配
        const keywordHit = matchKeyword(`${node?.title}${node?.connection?.ip}`, searchValue)
        if (!keywordHit && node.children) {
          node.children = filterNodesNotMatch(node.children)
        }
        return keywordHit || node.children?.length
      }),
    [searchValue],
  )

  const filteredTreeDataOnLocal = useMemo(() => {
    console.log('set filter TreeData')
    return searchValue ? filterNodesNotMatch(_.cloneDeep(newTreeData)) : newTreeData
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(newTreeData), searchValue])

  useEffect(() => {
    // 只获取包含search 的父节点
    if (searchValue) {
      let keys = getExpandKeysAboutContainSearchValue(filteredTreeDataOnLocal, searchValue)

      dispatch(setExpandedKeys(keys))
      // setAutoExpandParent(true)
    } else {
      dispatch(setExpandedKeys([newTreeData?.[0]?.key]))
    }

  }, [searchValue])

  return (
    <>
      {
        filteredTreeDataOnLocal?.length > 0 ? (
          <Tree
            className={styles.treeWrap}
            titleRender={treeTitleRender}
            treeData={filteredTreeDataOnLocal}
            onSelect={handleSelect}
            selectedKeys={selectedTreeNode ? [selectedTreeNode?.key] : []}
            onExpand={(expandedKeys) => dispatch(setExpandedKeys(expandedKeys))}
            expandedKeys={expandedKeys}
            loadData={handleLoadData}
            loadedKeys={loadedKeys || []}
            onLoad={(keys) => dispatch(setLoadedKeys(keys))}
          />
        ) : <div className='color667084 tc'>{t('flow_no_data')}</div>
      }
    </>
  )
}
export default TreeComponent