export const formatSelectDownloadData = (data: any, t: any) => {
  //转化为需要的格式
  const groupedData = data?.reduce((acc: any, item: any) => {
    // 查找是否已经存在该 toolTab 的对象
    const existingGroup: any = acc.find((group: any) => group.objectType === item.toolTab);

    if (existingGroup) {
      existingGroup.operations.push({
        operation: item.id,
        operationName: item.operateName,
        ...item
      });
    } else {
      // 如果不存在，则创建新的分组
      acc.push({
        objectType: item.toolTab,
        objectTypeName: item.toolTab === 'sdtMenu' ? t('systemManagement.system.other.sdtMenu') : item.toolTab === 'resultSetOperation' ? t('systemManagement.system.other.resultSetOperation') : t('systemManagement.system.other.exportFunction'),
        operations: [{
          operation: item.id,
          operationName: item.operateName,
          ...item
        }]
      });
    }

    return acc;
  }, []);
  return groupedData;
}