import { IValidTimeItem, ParameterIUnit } from 'src/api';
import { SPECIAL_ECHO_FIELD } from './constants';

export interface FormattedTimeDataItem  {
  label: string;
  value: any;
  timeValueUnit: ParameterIUnit ;
  timeValue: number;
}
//处理生效时间值，将小时转为天
export const formattedValidTimeData = (data?: IValidTimeItem[]): FormattedTimeDataItem[] => {
  if (!data) return [];
  const enabledTimes = data.filter((i: IValidTimeItem) => i?.timeStatus);
 
  return enabledTimes?.map(item => ({
    label: item?.timeName,
    //value是为了设置唯一值 使用了id  但是custom和forever 需要特殊处理 
    value: SPECIAL_ECHO_FIELD.includes(item?.timeValueUnit) ?  item?.timeValueUnit :  item?.id ,
    timeValue:   item?.timeValue,
    timeValueUnit: item?.timeValueUnit
  }));
}

//判断是否为永久生效时间
export const isForeverKey = (key: ParameterIUnit): boolean => {
  return key === "forever";
};

export const isCustomKey = (key: ParameterIUnit): boolean => {
  return key === "custom";
};