import React, { useState } from "react";
import { Form, Typography, Tooltip, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import classnames from 'classnames'
import {
  FormOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons'
import { useRequest } from 'src/hook'
import {
  getResultSetTipOption,
  setResultSetTipOption,
} from 'src/api';
import styles from '../index.module.scss'

const { Text } = Typography;


export const NoPermPromptFormItem = ({
  form,
  editing,
  setEditing,
  onSuccessCallback,
  onErrorCallback
}: {
  form: any
  editing: string
  setEditing: (v: string) => void
  onSuccessCallback: () => void
  onErrorCallback: (params: { resultSetTipOptions?: string[] }) => void
}) => {

  const { t } = useTranslation();
  //结果集选项配置
  const [resultSetTipOptions, setResultSetTipOptions] = useState<{ label: string; value: string }[]>([])

  //无权限提示入口设置
  const { data: resultSetTipOptionData, loading: resultSetTipOptionLoading, run: runGetResultSetTipOption } = useRequest(getResultSetTipOption, {
    onSuccess(res = []) {
      const val = res.filter(item => item?.selected).map(item => item?.key);
      form.setFieldsValue({ resultSetTipOption: val })
      setResultSetTipOptions(res.map((item) => ({ value: item.key, label: item.name })))
    }
  })
  // 无权限入口设置
  const { run: runSetResultSetTipOption } = useRequest(setResultSetTipOption, {
    manual: true,
    onSuccess() {
      onSuccessCallback();
      runGetResultSetTipOption();
    },
    onError() {
      const val = resultSetTipOptionData?.filter(item => item?.selected).map(item => item?.key);
      onErrorCallback({ resultSetTipOptions: val })
    },
  })

  const onSaveResultSetTipOption = () => {
    form.validateFields().then(({ resultSetTipOption = [] as string[] }) => {

      const options = resultSetTipOptions.map(option => ({ key: option?.value, name: option?.label, selected: resultSetTipOption?.includes(option?.value) }))
      runSetResultSetTipOption(options)
    })
  }

  return (
    <>
      {/* 无权限提示入口设置 */}
      <Form.Item label={
        <>
          {t('systemManagement.system.other.resultSetTipOption')} <Tooltip title={t('systemManagement.system.other.resultSetTipOption.extra')}>
            <QuestionCircleOutlined className={styles.pl5} />
          </Tooltip>
        </>
      }
      >
        <Form.Item
          name="resultSetTipOption"
          noStyle
          hidden={!(editing === 'resultSetTipOption')}
        >
          <Select
            showArrow
            allowClear
            mode='multiple'
            placeholder={t('systemManagement.system.other.resultSetTipOption.tip')}
            style={{ width: 380 }}
            options={resultSetTipOptions}
            autoFocus={true}
            onBlur={() => onSaveResultSetTipOption()}
          />
        </Form.Item>
        {editing !== 'resultSetTipOption' && (
          <div style={{ display: 'flex' }}>
            <Text className={classnames(styles.w300, styles.disInB)}>
              {resultSetTipOptionData?.filter(item => item.selected)?.map(item => item.name).join(',') || '-'}
            </Text>
            <FormOutlined onClick={() => { setEditing('resultSetTipOption') }} />
          </div>
        )}
      </Form.Item>
    </>
  )
}