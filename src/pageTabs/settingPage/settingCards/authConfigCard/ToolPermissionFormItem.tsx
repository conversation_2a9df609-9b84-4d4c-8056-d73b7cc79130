import React, { memo, useMemo, useState } from 'react';
import { Form, Typography, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import classnames from 'classnames'
import {
  FormOutlined,
} from '@ant-design/icons'
import { useRequest, useDispatch } from 'src/hook'
import {
  getExportSetting,
  getToolPermissionSetting,
  getAllToolPermissionSetting,
  updateToolPermissionSetting,
} from 'src/api';
import { setExportSetting } from 'src/appPages/login/loginSlice'
import ToolPermissionSelect from '../components/ToolPermissionSelect';
import { formatSelectDownloadData } from '../utils';
import styles from '../index.module.scss'

const { Text } = Typography

export const ToolPermissionFormItem = memo(({
  form,
  editing,
  setEditing,
  onSuccessCallback,
  onErrorCallback
}: {
  form: any
  editing: string | null;
  setEditing: (v: string) => void;
  onSuccessCallback: () => void;
  onErrorCallback: (data?: any) => void;
}) => {

  const { t } = useTranslation();
  const dispatch = useDispatch();

  //当前选中数据库实例
  const [databaseInstances, setDatabaseInstances] = useState([]);
  //权限工具 存储操作项[{label,value}]
  const [allTemplateOperations, setAllTemplateOperations] = useState<any>([]);
  //工具权限设置
  const { data: toolPermissionSettingData, run: runGetToolPermissionSetting } = useRequest(getToolPermissionSetting, {
    onSuccess(res) {
      const toolPermissionData = res?.reduce((acc: any, item: any) => {
        // 查找是否已经存在该 toolTab 的对象
        let existedgroup: any = acc.find((group: any) => group.objectType === item.toolTab);
        if (existedgroup) {
          existedgroup.operations.push(item.id);
        } else {
          // 如果不存在，则创建新的分组
          acc.push({
            objectType: item.toolTab,
            operations: [item.id]
          });
        }
        return acc;
      }, []);
      setDatabaseInstances(toolPermissionData);
      form.setFieldsValue({ toolPermission: res?.map((i: any) => i?.id) })
    }
  })

  // 获取导出设置
  const { run: runGetExportSetting } = useRequest(getExportSetting, {
    manual: true,
    onSuccess: (data) => {
      dispatch(setExportSetting(data))
    }
  })

  //更新是工具权限设置
  const { run: runUdateToolPermissionSetting, } = useRequest(updateToolPermissionSetting,
    {
      manual: true,
      onSuccess: () => {
        onSuccessCallback()
        runGetExportSetting()
        runGetToolPermissionSetting()
      },
      onError: () => {
        onErrorCallback({ toolPermission: toolPermissionSettingData?.map((i: any) => i?.id) })
      }
    }
  );

  //工具权限列表
  const { data: permissionTemplates } = useRequest(() => getAllToolPermissionSetting('ALL'), {
    onSuccess: (res = []) => {
      const formData: any = [];

      res.map((item: any) => {
        formData.push({ value: item?.id, label: item?.operateName, objectType: item?.toolTab })
      })
      setAllTemplateOperations(formData);
    },
  })

  const onSaveToolPermSetting = (values: any, isSubmit?: boolean) => {
    form.setFieldsValue({ databseInstance: values });

    if (isSubmit) {
      const defaultToolPermission = allTemplateOperations || [];

      runUdateToolPermissionSetting({
        toolPermissionSettings: defaultToolPermission.map((v: any) => {
          let selected = false;
          if (values?.includes(v?.value)) {
            selected = true;
          }
          return {
            id: v.value,
            selected
          }
        })
      })
    }
  }

  const toolPermissionText = useMemo(() => {
    if (toolPermissionSettingData?.length) {
      return toolPermissionSettingData?.map((i: any) => i?.operateName).join(',')
    }
    else return ''
  }, [JSON.stringify(toolPermissionSettingData)])

  return (
    <>
      {/* 工具权限设置 */}
      <Form.Item
        name='toolPermission'
        label={t('systemManagement.system.other.toolPermission')}
      >
        {
          editing === 'toolPermission' ?
            <ToolPermissionSelect
              loading={false}
              options={allTemplateOperations}
              dropdownTemplates={formatSelectDownloadData(permissionTemplates, t)}
              selectedValues={databaseInstances}
              onChange={(values: number[], a: any, isSubmit?: boolean) => {
                onSaveToolPermSetting(values, isSubmit)
              }}
              onCancel={() => {
                setEditing('');
                form.setFieldsValue({ toolPermission: toolPermissionSettingData?.map((i: any) => i?.id) })
              }}
            />
            :
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Text className={classnames(styles.w300, styles.disInB)}>
                <Tooltip title={toolPermissionText}>
                  {toolPermissionText?.replace(/^(.{20}).+$/, (match: any, p1: string) => p1 + '...')}
                </Tooltip>
              </Text>
              <FormOutlined onClick={() => { setEditing('toolPermission') }} />
            </div>
        }
      </Form.Item>
    </>
  );
});