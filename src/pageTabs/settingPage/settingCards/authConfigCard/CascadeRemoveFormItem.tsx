import React from 'react';
import classnames from 'classnames'
import { Form, Select, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { FormOutlined } from '@ant-design/icons';
import { useRequest } from 'src/hook';
import {
  setCascadeDefault,
  getCascadeDefault
} from 'src/api'
import styles from '../index.module.scss'

const { Text } = Typography;

export const CascadeRemoveFormItem = ({
  form,
  editing,
  setEditing,
  onSuccessCallback,
  onErrorCallback
}: {
  form: any
  editing: string
  setEditing: (key: string) => void
  onSuccessCallback: () => void
  onErrorCallback: (data: any) => void
}) => {
  // const dispatch = useDispatch();
  const { t } = useTranslation();

  //级联
  const { data: cascadeDefaultData, loading: cascadeDefaultLoading, run: runGetCascadeDefault } = useRequest(getCascadeDefault, {
    onSuccess: (cascadeRemoval) => {
      form.setFieldsValue({ cascadeRemoval: cascadeRemoval === false ? 0 : 1 })
    }
  })

  // 设置级联移除设置
  const { loading: updateCascadeLadoing, run: runSetCascadeSwitch } = useRequest(setCascadeDefault, {
    manual: true,
    onSuccess() {
      onSuccessCallback();
      runGetCascadeDefault();
    },
    onError() {
      onErrorCallback({ cascadeRemoval: cascadeDefaultData === false ? 0 : 1 })
    },
  })

  return (
    <>
      <Form.Item label={t('systemManagement.system.other.cascadeRemoval.label')}>
        <Form.Item
          name='cascadeRemoval'
          noStyle
          hidden={!(editing === 'cascadeRemovalSetting')}
        >
          <Select onChange={(v) => runSetCascadeSwitch(!!v)} style={{ width: 380 }}>
            <Select.Option value={1}>{t('common.text.cascadeRemoval')}</Select.Option>
            <Select.Option value={0}>{t('common.text.nonCascadeRemoval')}</Select.Option>
          </Select>
        </Form.Item>
        {editing !== 'cascadeRemovalSetting' && (
          <div style={{ display: 'flex' }}>
            <Text className={classnames(styles.w300, styles.disInB)}>{cascadeDefaultData === false ? t('common.text.nonCascadeRemoval') : t('common.text.cascadeRemoval')}</Text>
            <FormOutlined onClick={() => setEditing('cascadeRemovalSetting')} />
          </div>
        )}
      </Form.Item>
    </>
  )
}