import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import AddOrEditPermPeriodModal from './AddOrEditAuthPeriodModal';

// Mock配置
beforeAll(() => {
  // 处理matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: (query: string) => ({
      matches: query.includes('max-width'),
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    }),
  });
});

// Mock Ant Design组件
jest.mock('antd', () => {
  const antd = jest.requireActual('antd');
  return {
    ...antd,
    Modal: {
      ...antd.Modal,
    },
    Form: {
      ...antd.Form,
      useForm: () => [{
        validateFields: jest.fn().mockImplementation((callback) => callback(null, {})),
        getFieldValue: jest.fn(),
        setFieldsValue: jest.fn(),
      }, jest.fn()],
    },
  };
});

describe('AddOrEditPermPeriodModal弹框测试', () => {
  const mockOnCancel = jest.fn();
  const mockOnRefresh = jest.fn();

  it('应正确渲染Modal和按钮', () => {
    render(
      <AddOrEditPermPeriodModal
        editPermPeriodItem={{ name: '测试', parameter: { time: 10, unit: '1' }}} //unit为测试数据
        onCancel={mockOnCancel}
        onRefresh={mockOnRefresh}
      />
    );

    // 确认Modal已渲染
 
    let antdModal = document.querySelector(".ant-modal-root");
    expect(antdModal).toBeInTheDocument();
    // 查找按钮 - 使用更灵活的方式
    const buttons = screen.getAllByRole('button');
    const okButton = buttons.find(btn => btn.textContent?.includes('确 定'));
    const cancelButton = buttons.find(btn => btn.textContent?.includes('取 消'));
    
    expect(okButton).toBeInTheDocument();
    expect(cancelButton).toBeInTheDocument();
  });

  it('显示初始表单值', () => {
    render(
      <AddOrEditPermPeriodModal
        editPermPeriodItem={{ name: '新的时间', parameter: { time: 10, unit: '1' }}} //unit为测试数据
        onCancel={mockOnCancel}
        onRefresh={mockOnRefresh}
      />
    );

    expect(screen.getByDisplayValue('新的时间')).toBeInTheDocument();
    expect(screen.getByDisplayValue('10')).toBeInTheDocument();
  });

  it('名称不为空校验', async () => {
    render(<AddOrEditPermPeriodModal editPermPeriodItem={null} onCancel={mockOnCancel}  onRefresh={mockOnRefresh}/>);
    
    // 找到名称输入框 - 通过placeholder
    const nameInput = screen.getByPlaceholderText('请输入名称');
    expect(nameInput).toBeInTheDocument();
    
    // 清空输入框
    fireEvent.change(nameInput, { target: { value: '' } });
    
    // 点击确定按钮
    const submitButton = screen.getByText((content, element) => 
    content.includes('确 定')
  );
    fireEvent.click(submitButton);
    
    expect(await screen.findByText('请输入名称')).toBeInTheDocument();
  });

  it('名称只能由中文、数字、英文组成，2-24字校验', async () => {
    render(<AddOrEditPermPeriodModal editPermPeriodItem={null} onCancel={mockOnCancel}  onRefresh={mockOnRefresh}/>);
    
    // 名称输入框
    const nameInput = screen.getByPlaceholderText('请输入名称');
    
    // 输入非法名称 则触发校验
    fireEvent.change(nameInput, { target: { value: 'test@#$' } });
    
    // 点击确定按钮
    const submitButton = screen.getByText((content, element) => 
      content.includes('确 定')
    );
    
    fireEvent.click(submitButton);
    
    // 检查错误信息是否显示
    expect(await screen.findByText('只能由中文、数字、英文组成，2-24字')).toBeInTheDocument();
  });

  it('参数值-只能输入大于等于1的整数校验', async () => {
    render(<AddOrEditPermPeriodModal editPermPeriodItem={null} onCancel={mockOnCancel}  onRefresh={mockOnRefresh}/>);
    
    // 参数值输入框
    const timeInputs = screen.getAllByRole('textbox');
    const timeInput = timeInputs.find(input => 
      !(input as HTMLInputElement).placeholder
    ) as HTMLInputElement;
    
    // 输入无效时间
    fireEvent.change(timeInput, { target: { value: '0' } });
    
    const submitButton = screen.getByText((content, element) => 
      content.includes('确 定')
    );
    
    submitButton && fireEvent.click(submitButton);
    
    // 检查参数值错误信息是否显示
    expect(await screen.findByText('只能输入大于等于1的整数')).toBeInTheDocument();
  });
});