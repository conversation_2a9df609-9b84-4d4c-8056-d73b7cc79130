import React, { useEffect } from "react";
import { useTranslation } from 'react-i18next';
import { Modal, Form, Input, Select, Switch, message } from 'antd';
import { useRequest } from 'src/hook'
import {
  addValidaTime,
  updateValidTime,
  ParameterIUnit
} from 'src/api'
import { PERM_PERIOD_PARAMETER_UNIT, ALL_VALID_TIME_UNIT } from './constants';

export const FormLayoutWithFour = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
}
export default ({
  editPermPeriodItem,
  onCancel,
  onRefresh: onRefreshList
}: {
  editPermPeriodItem: any
  onCancel: () => void;
  onRefresh: () => void;
}) => {

  const [form] = Form.useForm();
  const { t } = useTranslation();
  //参数值时间单位
  const unitOptions = PERM_PERIOD_PARAMETER_UNIT.map(filed => ({label: ALL_VALID_TIME_UNIT[filed] , value: filed}))

  //新增 或更新 有效时间配置
  const { loading: addOrUpdateBtnLoading, run: runAddOrUpdateValidaTime } = useRequest((params) => {
    if (editPermPeriodItem) {
      return updateValidTime({ ...params, id: editPermPeriodItem?.id });
    }
    return addValidaTime(params);
  }, {
    manual: true,
    onSuccess: () => {

      if (editPermPeriodItem) {
        message.success(t('common.message.edit.success'));
      } else {
        message.success(t('common.message.new_success'));
      }
      onCancel();
      onRefreshList();
    },
    onError: (res) => {
      //名字校验重复 回显在form表单问题，后端无法增加特殊标识，目前这样处理
      if (typeof res === 'string' && res === t('systemManagement.system.authMenu.name.hint')) {
        //重命名校验
        form.setFields([{ name: 'timeName', errors: [res] }])
      }
    }
  })

  useEffect(() => {
    form.setFieldsValue({ ...editPermPeriodItem })

  }, [form, editPermPeriodItem])

  const onValidateForm = () => {
    form.validateFields().then(values => {

      runAddOrUpdateValidaTime(values)
    }).catch(info => {
      console.log('Validate Failed:', info);
    });
  }

  return (
    <Modal
      centered
      visible={true}
      title={editPermPeriodItem ? t('common.btn.edit') : t('common.btn.add')}
      onOk={onValidateForm}
      onCancel={() => onCancel()}
      okText={t('common.btn.ok')}
      cancelText={t('common.btn.cancel')}
      okButtonProps={{
        loading: addOrUpdateBtnLoading
      }}
    >
      <Form form={form} {...FormLayoutWithFour}>
        <Form.Item
          label={t('systemManagement.system.authMenu.name.label')}
          required
          name='timeName'
          rules={[
            {
              required: true, message: t('systemManagement.system.authMenu.name.plac')
            },
            {
              pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]{2,24}$/,
              message: t('systemManagement.system.authMenu.name.hint2'),
            }
          ]}
        >
          <Input  placeholder={t('systemManagement.system.authMenu.name.plac')} />
        </Form.Item>
        <Form.Item
          required
          label={t('systemManagement.system.authMenu.parameter.label')}
        >
          <Input.Group compact>
            <Form.Item
              noStyle
              name='timeValue'
              dependencies={['timeValueUnit']}
              rules={[
                {
                  required: true,
                  message: t('systemManagement.system.authMenu.parameter.hint')
                },
                {
                  pattern: /^[1-9]\d*$/,
                  message: t('systemManagement.system.authMenu.parameter.hint2')
                },
              ]}
            >
              <Input style={{ width: '40%' }} />
            </Form.Item>
            <Form.Item noStyle name='timeValueUnit' initialValue={'day' as ParameterIUnit}>
              <Select style={{ width: '60%' }} options={unitOptions} />
            </Form.Item>
          </Input.Group>
        </Form.Item>
        <Form.Item required name="timeStatus" label={t('systemManagement.system.authMenu.status.label')} initialValue={true}>
          <Switch defaultChecked />
        </Form.Item>
      </Form>
    </Modal>
  )
}