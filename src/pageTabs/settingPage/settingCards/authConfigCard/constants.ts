import i18n from "i18next";
import { ParameterIUnit } from 'src/api'
// 权限期限自定义 参数值单位

export const ALL_VALID_TIME_UNIT: Record<ParameterIUnit, string> = {
  day: i18n.t('systemManagement.system.authMenu.parameter.day'),
  hour: i18n.t('systemManagement.system.authMenu.parameter.hour'),
  forever: i18n.t('common.effectTime.forever'),
  custom: i18n.t('systemManagement.system.authMenu.customAuthPeriod.custom')
};

// 权限期限自定义 参数值单位（下拉框）
export const PERM_PERIOD_PARAMETER_UNIT: Array<keyof typeof ALL_VALID_TIME_UNIT> = [
  'day',
  'hour'
]

// 定义单个类型，只能是 PERM_PERIOD_PARAMETER_UNIT 中的值之一
export type PermPeriodParameterUnit = typeof PERM_PERIOD_PARAMETER_UNIT[number];

// 列表 参数值特殊回显字段，不显示数值
export const SPECIAL_ECHO_FIELD:Array<keyof typeof ALL_VALID_TIME_UNIT>  = [
  'custom', 
  'forever'
]