import React, { memo, useState } from 'react';
import { Form, message, Tooltip, Switch, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useRequest, useSelector } from 'src/hook';
import {
  postApplyResourceSearchSetting,
  getApplyResourceSearchSetting
} from 'src/api';
import { FormLayout } from '../../constants'
import { ToolPermissionFormItem } from './ToolPermissionFormItem';
import { NoPermPromptFormItem } from './NoPermPromptFormItem';
import { CascadeRemoveFormItem } from './CascadeRemoveFormItem';
import { CustomAuthPeriodFormItem } from './CustomAuthPeriodFormItem'
import styles from '../index.module.scss'

export const AuthConfigCard = memo(() => {

  const { t } = useTranslation();
  const [form] = Form.useForm();

  const isLangEn = useSelector(state => state.login.locales) === 'en';

  const [editing, setEditing] = useState<string>('');

  //访问申请页面展示所有资源
  const { data: settingData, loading: applyResourceSearchSettingLoading, run: runGetApplyResourceSearchSetting } = useRequest(getApplyResourceSearchSetting, {
    onSuccess(setting) {
      form.setFieldsValue({ setting })
    }
  })
  
  const { run: postApplyResourceSearchSettingRun } = useRequest(postApplyResourceSearchSetting, {
    manual: true,
    onSuccess() {
      onSuccessCallback()
      runGetApplyResourceSearchSetting()
    },
    onError() {
      onErrorCallback({ setting: settingData })
    },
  })

  const onErrorCallback = (fileInit: any) => {
    setEditing('')
    form.setFieldsValue(fileInit)
  }

  const onSuccessCallback = () => {
    message.success(t('common.message.editSuccess'))
    setEditing('')
  }

  return (
    <section className="cq-new-card flow-card" id="permissionConfigCard">
      <div className="cq-card__headerbar">
        <h3 className="cq-card__title">{t('systemManagement.system.authMenu.title')}</h3>
      </div>
      <section className="card__content" style={{ padding: '0 16px' }}>
        <Spin spinning={applyResourceSearchSettingLoading} >
          <Form form={form} {...FormLayout} labelCol={{span: isLangEn ? 8 : 5 }}>
            {/* 工具权限设置 */}
            <ToolPermissionFormItem
              form={form}
              editing={editing}
              setEditing={(v: string) => setEditing(v)}
              onSuccessCallback={onSuccessCallback}
              onErrorCallback={onErrorCallback}
            />
            {/* 工具权限设置 */}
            <NoPermPromptFormItem
              form={form}
              editing={editing}
              setEditing={(v: string) => setEditing(v)}
              onSuccessCallback={onSuccessCallback}
              onErrorCallback={onErrorCallback}
            />
            {/* 级联 */}
            <CascadeRemoveFormItem
              form={form}
              editing={editing}
              setEditing={(v: string) => setEditing(v)}
              onSuccessCallback={onSuccessCallback}
              onErrorCallback={onErrorCallback}
            />
            {/* 访问申请页面展示所有资源 */}
            <Form.Item
              label={
                <>
                  {t('systemManagement.system.other.setting')}
                  <Tooltip title={t('systemManagement.system.other.setting.tip')}>
                    <QuestionCircleOutlined className={styles.pl5} />
                  </Tooltip>
                </>
              }
            >
              <Form.Item
                noStyle
                name='setting'
                valuePropName='checked'
              >
                <Switch onChange={(v) => postApplyResourceSearchSettingRun(v)} />
              </Form.Item>
            </Form.Item >
            <CustomAuthPeriodFormItem/>
          </Form>
        </Spin>
      </section>
    </section>
  );
});