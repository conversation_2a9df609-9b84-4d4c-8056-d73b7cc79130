const extractSchemaInfo = (nodeInfo: any) => {
  if (!nodeInfo || typeof nodeInfo !== 'object') {
      return null;
  }
  
  const { nodePath, nodePathWithType } = nodeInfo;
  
  // 从nodePathWithType中提取schema名称
  const schemaSegment = (nodePathWithType?.split('/') || [])
      .find((seg: any) => seg.startsWith('SCHEMA:'));
  
  if (!schemaSegment) return null;
  
  const schemaName = schemaSegment.split(':')[1];
  if (!schemaName) return null;
  
  // 截取nodePath到schema部分
  const pathSegments = (nodePath || '').split('/');
  const schemaIndex = pathSegments.findIndex((seg: any) => seg === schemaName);
  
  if (schemaIndex === -1) return null;
  
  const truncatedNodePath = pathSegments.slice(0, schemaIndex + 1).join('/');
  
  // 截取nodePathWithType到schema部分
  const typeSegments = (nodePathWithType || '').split('/');
  const schemaTypeIndex = typeSegments.findIndex((seg: any) => seg.startsWith('SCHEMA:'));
  
  if (schemaTypeIndex === -1) return null;
  
  const truncatedNodePathWithType = typeSegments.slice(0, schemaTypeIndex + 1).join('/');
  
  return {
      nodeName: schemaName,
      nodePath: truncatedNodePath,
      nodePathWithType: truncatedNodePathWithType
  };
}

export default extractSchemaInfo