import React from 'react'
import { useSelector } from 'src/hook'
import { Col } from 'antd'
import { NodeDetailPane } from '../nodeDetailPane/NodeDetailPane'
import { ApplicationShopping } from '../applicationShopping'
import { ErrorBoundary } from 'src/components'
import styles from '../index.module.scss'

export const NodeDetailPaneController: React.FC = () => {
  const { tabInfoMap, activeTabKey } = useSelector(
    (state) => state.queryTabs,
  )
  const activePaneType = tabInfoMap[activeTabKey]?.paneType
  const compareVersion = activePaneType === 'diff'

  // 版本对比时隐藏此部分
  if (compareVersion) {
    return null
  }

  return (
    <Col className={styles.nodeDetailColumn}>
      <ErrorBoundary>
        <NodeDetailPane />
        {/* <TaskPane /> */}
        <ApplicationShopping />
      </ErrorBoundary>
    </Col>
  )
}