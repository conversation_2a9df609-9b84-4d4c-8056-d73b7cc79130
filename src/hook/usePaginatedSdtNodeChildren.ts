import { useState, useMemo, useEffect, useCallback } from 'react'
import { useSelector, useDispatch } from 'src/hook'
import { fetchTreeNodeChildren } from 'src/pageTabs/queryPage/sdt'
import { getUserConfig } from 'src/api'
import type { NodeEntity } from 'src/types'

interface PaginatedNodeData {
  data: NodeEntity[]
  hasMore: boolean
  loading: boolean
  loadMore: () => Promise<void>
}

export const usePaginatedSdtNodeChildren = (parent?: NodeEntity): PaginatedNodeData => {
  const dispatch = useDispatch()
  const treeNodeChildrenMap = useSelector(
    (state) => state.sdt.treeNodeChildrenMap,
  )
  const groupByType = useSelector(
    (state) => state.sdt.groupByType,
  )
  
  const [loading, setLoading] = useState<boolean>(false)
  const [loadingMore, setLoadingMore] = useState<boolean>(false)
  const [pageNo, setPageNo] = useState<number>(1)
  const [pageSize, setPageSize] = useState<number>(100) // 默认值
  const [allData, setAllData] = useState<NodeEntity[]>([])
  const [hasMore, setHasMore] = useState<boolean>(false)

  // 判断是否应该支持分页，遵循Sdt.tsx中的逻辑
  const shouldSupportPaging = useMemo(() => {
    if (!parent) return false
    
    const { supportPaging, connectionType, nodeType } = parent
    
    // 1. 子级支持分页
    if (supportPaging) return true
    
    // 2. Redis连接特殊处理
    if (connectionType === 'Redis' && nodeType === 'connection') return true
    
    // 3. 其他情况不支持分页
    return false
  }, [parent])

  // 获取用户配置的分页大小
  useEffect(() => {
    const fetchUserConfig = async () => {
      try {
        const { sdtPageSize } = await getUserConfig()
        setPageSize(sdtPageSize ?? 100)
      } catch (error) {
      }
    }
    fetchUserConfig()
  }, [])

  // 获取缓存的数据
  const cachedData = useMemo(() => {
    if (parent) {
      const memo = treeNodeChildrenMap[parent.nodePath]
      return memo?.filter(({ switchable }) => switchable !== false) || []
    }
    return []
  }, [parent, treeNodeChildrenMap])

  // 加载数据的函数
  const loadData = useCallback(async (page: number, isLoadMore: boolean = false) => {
    if (!parent) return

    if (isLoadMore) {
      setLoadingMore(true)
    } else {
      setLoading(true)
    }

    try {
      let params = {
        ...parent,
        groupByType,
        isSdtNode: false,
        notMemorize: true, // 分页数据不缓存
      } as any

      // 只有支持分页的数据源才传递分页参数，遵循Sdt.tsx的逻辑
      if (shouldSupportPaging) {
        const { supportPaging, connectionType, nodeType } = parent
        
        if (supportPaging) {
          // 子级支持分页，使用用户配置的分页大小
          params = {
            ...params,
            pageSize,
            pageNo: page,
            startPageNum: page,
          }
        } else if (connectionType === 'Redis' && nodeType === 'connection') {
          // Redis特殊处理，固定pageSize=10
          params = {
            ...params,
            pageSize: 10,
            pageNo: page,
            startPageNum: page,
          }
        }
      }

      const result = await dispatch(fetchTreeNodeChildren(params)).unwrap()
      const newData = result?.data || []

      if (isLoadMore) {
        // 加载更多时追加数据，去重处理
        setAllData(prev => {
          const existingPaths = new Set(prev.map(item => item.nodePath))
          const uniqueNewData = newData.filter(item => !existingPaths.has(item.nodePath))
          return [...prev, ...uniqueNewData]
        })
      } else {
        // 首次加载时替换数据
        setAllData(newData)
      }

      // 只有支持分页的数据源才判断是否还有更多数据
      if (shouldSupportPaging) {
        const effectivePageSize = parent?.connectionType === 'Redis' && parent?.nodeType === 'connection' ? 10 : pageSize
        setHasMore(newData.length >= effectivePageSize)
      } else {
        // 不支持分页的数据源，hasMore永远为false
        setHasMore(false)
      }
      
    } catch (error) {
      console.error('Failed to load database list:', error)
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }, [parent, dispatch, groupByType, pageSize, shouldSupportPaging])

  // 加载更多数据
  const loadMore = useCallback(async () => {
    if (loadingMore || !hasMore) {
      return
    }

    const nextPage = pageNo + 1
    setPageNo(nextPage)
    await loadData(nextPage, true)
  }, [loadData, pageNo, loadingMore, hasMore])

  // 初始加载
  useEffect(() => {
    if (parent && pageSize > 0) {
      // 如果不支持分页，直接使用缓存数据
      if (!shouldSupportPaging && cachedData.length > 0) {
        setAllData(cachedData)
        setHasMore(false)
        setPageNo(1)
        return
      }
      
      // 支持分页的情况下，如果有缓存数据且数量小于分页大小，直接使用缓存
      if (shouldSupportPaging && cachedData.length > 0 && cachedData.length < pageSize) {
        setAllData(cachedData)
        setHasMore(false)
        setPageNo(1)
      } else {
        // 否则重新加载第一页
        setPageNo(1)
        loadData(1, false)
      }
    }
  }, [parent, pageSize, cachedData.length, loadData, shouldSupportPaging])

  // 根据是否支持分页来决定最终返回的数据
  const finalData = useMemo(() => {
    // 不支持分页的数据源，直接使用缓存数据
    if (!shouldSupportPaging && cachedData.length > 0) {
      return cachedData
    }
    
    // 支持分页的数据源，如果有缓存数据且数量小于分页大小，使用缓存数据
    if (shouldSupportPaging && cachedData.length > 0 && cachedData.length < pageSize) {
      return cachedData
    }
    
    return allData
  }, [cachedData, allData, pageSize, shouldSupportPaging])

  const finalHasMore = useMemo(() => {
    // 不支持分页的数据源，hasMore永远为false
    if (!shouldSupportPaging) {
      return false
    }
    
    // 支持分页的数据源，如果有缓存数据且数量小于分页大小，hasMore为false
    if (cachedData.length > 0 && cachedData.length < pageSize) {
      return false
    }
    
    return hasMore
  }, [cachedData.length, pageSize, hasMore, shouldSupportPaging])

  const finalLoading = useMemo(() => {
    // 不支持分页的数据源，如果有缓存数据就不显示loading
    if (!shouldSupportPaging && cachedData.length > 0) {
      return false
    }
    
    // 支持分页的数据源，如果有缓存数据且数量小于分页大小，不显示loading
    if (shouldSupportPaging && cachedData.length > 0 && cachedData.length < pageSize) {
      return false
    }
    
    return loading
  }, [cachedData.length, pageSize, loading, shouldSupportPaging])

  return {
    data: finalData,
    hasMore: finalHasMore,
    loading: finalLoading || loadingMore,
    loadMore,
  }
}
